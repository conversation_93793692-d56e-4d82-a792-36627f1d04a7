/*
 * @Author: hong<PERSON><EMAIL>
 * @Date: 2025-07-16 11:42:08
 * @LastEditTime: 2025-08-14 16:13:59
 */
import {FC, useCallback, useContext, useEffect, useState, useRef} from 'react';
import {Form, Input, Link, Modal, toast, Radio, Select} from 'acud';
import {RULE} from '@utils/regs';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import * as http from '@api/metaRequest';

import flags from '@/flags';
import {TableSource, IcebergTableTypeMap, ETableIceberg, DorisTableTypeMap, ETableDoris} from '../../const';
import styles from './index.module.less';
import {TableHanleModel} from '@api/meta/table';
interface CreateTableBaseInfoProps {
  tableFrom: string;
  onChange: (values?: any) => void;
  onModelType?: (value: any) => void;
  tableHanleWay: TableHanleModel;
  dataInfo: any;
}

const isPrivate = flags.DatabuilderPrivateSwitch;

// 跳转accesslist页面
const jumpAccesslist = () => {
  window.open('/iam/#/iam/accesslist', '_blank');
};

const CreateTableBaseInfo: FC<CreateTableBaseInfoProps> = ({
  tableFrom,
  onChange,
  onModelType,
  tableHanleWay,
  dataInfo
}) => {
  // 使用replace 防止破坏history
  const [{catalog, schema, node}] = useUrlState(undefined, {
    navigateMode: 'replace'
  });
  const {workspaceId} = useContext(WorkspaceContext);

  const [tableType, setTableType] = useState(ETableIceberg.MANAGED);

  // 防抖定时器引用
  const debounceTimerRef = useRef<number | null>(null);

  // 防抖验证表名是否存在
  const debouncedValidateTableName = useCallback(
    (value: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // 清除之前的定时器
        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
        }

        // 设置新的定时器，1秒后执行验证
        debounceTimerRef.current = window.setTimeout(async () => {
          try {
            const res = await http.getTableDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
            if (res.success && res.result?.id) {
              reject(new Error(`该数据表名称已存在，请重新输入`));
            } else {
              resolve();
            }
          } catch {
            // 表详情获取失败，意味着库中无此表，可用该表名称
            resolve();
          }
        }, 1000);
      });
    },
    [workspaceId, catalog, schema]
  );

  useEffect(() => {
    setTableType(dataInfo?.tableType || ETableIceberg.MANAGED);
  }, [dataInfo]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 是否 编辑模式
  const isEdit = tableHanleWay === TableHanleModel.EDIT;

  return (
    <div className={styles['bash_info_container']}>
      <div className={styles['bash_info_desc']}>基本信息</div>
      <Form.Item label="数据源格式" name="dataSourceFormat" initialValue={tableFrom}>
        {tableFrom + (tableFrom === TableSource.DORIS ? '内表' : '')}
      </Form.Item>

      <Form.Item
        label="数据表名称"
        name="name"
        validateFirst
        rules={[
          {required: true, message: `请输入数据表名称`},
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialNameStartEn128.test(value)) {
                return Promise.reject(new Error(RULE.specialNameStartEn128Text));
              }
              // 编辑模式下，未改动表名无需校验
              if (isEdit && node === value) {
                return Promise.resolve();
              }
              // 使用防抖验证表名是否存在
              return debouncedValidateTableName(value);
            }
          }
        ]}
      >
        <Input placeholder={`请输入数据表名称`} allowClear limitLength={128} onChange={onChange} />
      </Form.Item>

      {TableSource.ICEBERG === tableFrom && (
        <Form.Item
          label="数据表类型"
          name="tableType"
          required
          tooltip="内部表的数据存储在系统默认路径中，由平台统一管理，删除表会同时删除数据；外部表需指定外部存储路径，数据保留在用户自定义位置，删除表不会删除数据。"
          initialValue={ETableIceberg.MANAGED}
        >
          <Radio.Group
            disabled={isEdit}
            onChange={(e: any) => {
              onChange(e.target.value);
              setTableType(e.target.value);
            }}
          >
            {Object.keys(IcebergTableTypeMap).map((key) => (
              <Radio value={key} key={key}>
                <span>{IcebergTableTypeMap[key]}</span>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      )}

      {TableSource.DORIS === tableFrom && (
        <Form.Item
          style={{display: 'none'}}
          label="数据表类型"
          name="tableType"
          required
          tooltip="内部表的数据存储在工作空间的元存储中，外部表需要用户挂载其他BOS路径，数据存储在对应BOS中"
          initialValue={ETableIceberg.MANAGED}
        ></Form.Item>
      )}

      {tableType === ETableIceberg.EXTERNAL &&
        (isPrivate ? (
          <Form.Item
            label="存储位置"
            name="storageLocation"
            rules={[
              {required: true, message: '请输入HDFS路径'},
              {pattern: RULE.hdfsDefaultFs, message: RULE.hdfsText},
              {
                validator: async (_, value) => {
                  if (value.length > 493) {
                    return Promise.reject(new Error('HDFS路径长度不能超过500'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
            keepDisplayExtra
            extra={
              <div>
                <span>
                  填写hdfs://fs.defaultFS参数值/目录名称，其中“fs.defaultFS参数值”从HDFS的core-site.xml文件中获取
                </span>
              </div>
            }
          >
            <Input disabled={isEdit} placeholder="请输入HDFS路径" addonBefore="hdfs://" />
          </Form.Item>
        ) : (
          <>
            <Form.Item
              label="BOS路径"
              name="storageLocation"
              rules={[
                {required: true, message: '请输入BOS路径'},
                {pattern: RULE.bos, message: RULE.bosText},
                {
                  validator: async (_, value) => {
                    if (value.length > 494) {
                      return Promise.reject(new Error('BOS路径长度不能超过500'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
              keepDisplayExtra
              extra={
                <div>
                  <span>BOS路径需要和工作空间在同一个地域</span>
                </div>
              }
            >
              <Input disabled={isEdit} placeholder="请输入BOS路径" addonBefore="bos://" />
            </Form.Item>
            <Form.Item
              label="AccessKey"
              name="accessKeyId"
              rules={[{required: true, message: '请输入AccessKey'}]}
              keepDisplayExtra
              extra={
                <div>
                  <span>建议AK/SK有BOSFullControl权限，权限不足会影响Volume的读写操作。</span>
                  <Link onClick={jumpAccesslist}>立即获取</Link>
                </div>
              }
            >
              <Input disabled={isEdit} placeholder="请输入AccessKey" allowClear />
            </Form.Item>

            <Form.Item
              label="SecretKey"
              name="secretAccessKey"
              rules={[{required: true, message: '请输入SecretKey'}]}
            >
              <Input disabled={isEdit} placeholder="请输入SecretKey" allowClear />
            </Form.Item>
          </>
        ))}

      {TableSource.DORIS === tableFrom && (
        <Form.Item
          label="数据表模型"
          name="tableModel"
          required
          tooltip="明细模型：保留所有写入记录，不做去重，适合日志、行为数据；唯一模型：相同主键列的记录仅保留一条，适合更新场景，如订单表；聚合模型：按指定列做预聚合，适合报表汇总场景，如按天统计表。"
          initialValue={ETableDoris.DUPLICATE}
        >
          <Radio.Group
            disabled={isEdit}
            onChange={(e: any) => {
              onChange(e.target.value);
              onModelType(e.target.value);
            }}
          >
            {Object.keys(DorisTableTypeMap).map((key) => (
              <Radio value={key} key={key}>
                <span>{DorisTableTypeMap[key]}</span>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      )}

      <Form.Item label="描述" name="comment">
        <Input.TextArea
          placeholder="请输入数据表描述"
          allowClear
          limitLength={300}
          autoSize={{minRows: 2, maxRows: 4}}
          onChange={onChange}
        />
      </Form.Item>
    </div>
  );
};

export default CreateTableBaseInfo;
