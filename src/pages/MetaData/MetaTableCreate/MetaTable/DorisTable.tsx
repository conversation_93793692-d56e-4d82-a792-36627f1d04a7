/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-15 11:40:32
 * @LastEditTime: 2025-08-14 16:02:39
 */

import CreateTableBaseInfo from '../components/CreateTableBaseInfo';
import LeftContentHeader from '../components/LeftContentHeader';
import {Form, Input, Select, Checkbox, Radio, Alert, InputNumber, Tooltip, Loading} from 'acud';
import styles from './index.module.less';
import {TableSource, DorisTableBucketMap, ETableDorisBucket, ETableDoris} from '../const';
import TableEditList from '../components/TableEditList';
import {DorisTableFieldTypeMap, DorisAggFuncMap, DorisFieldTypes, TableHanleModel} from '@api/meta/table';
import {useState, useEffect, useMemo, useContext, useCallback} from 'react';
import {FormInstance} from 'acud/lib/form';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {useRequest} from 'ahooks';
import {WorkspaceContext} from '@pages/index';
import useUrlState from '@ahooksjs/use-url-state';
import {detailDataFormate} from '../utils';
import {RULE} from '@utils/regs';

interface MetaDorisTableProps {
  // TODO: Props 每一个数据都应该要加注释
  form: FormInstance<any>;
  onSilderHandle: () => void;
  onChange: () => void;
  status: string;
  errMsg: string;
  tableHanleWay: string;
}

// 字段类型
const grayStatus = [
  DorisFieldTypes.ARRAY,
  DorisFieldTypes.MAP,
  DorisFieldTypes.STRUCT,
  DorisFieldTypes.VARIANT,
  DorisFieldTypes.HLL,
  DorisFieldTypes.BITMAP,
  DorisFieldTypes.QUANTILE_STATE,
  DorisFieldTypes.AGG_STATE
];

const MetaDorisTable = (props: MetaDorisTableProps) => {
  const {form, status, errMsg, tableHanleWay, onSilderHandle, onChange} = props;
  // 基础信息
  const [{catalog, schema, node}] = useUrlState(undefined, {
    navigateMode: 'replace'
  });

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // table 详情数据
  const [dataInfo, setDataInfo] = useState<http.ITableDetailRes>();

  // 是否是编辑模型
  const editTag = tableHanleWay === TableHanleModel.EDIT;

  const [modelType, setModelType] = useState('');

  const bucketFields = Form.useWatch('columns', form) || [];

  // 基础内容逻辑
  const selectTypeRenderComponent = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curItem = formValues?.columns?.[index] || {};
      const isAddTag = dataInfo?.columns?.[index] ? false : true;

      const [typeScale, setTypeScale] = useState(10);

      const initialType = formValues?.columns?.[index]?.typeName || DorisFieldTypes.STRING;

      const typeMatchField = useMemo(() => {
        switch (initialType) {
          case DorisTableFieldTypeMap.CHAR:
            return (
              <div className={styles['select_render_filed']}>
                <div> ( </div>
                <Form.Item
                  rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
                  name={[index, 'typeLength']}
                  initialValue={10}
                >
                  <InputNumber
                    disabled={!isAddTag && editTag}
                    min={1}
                    max={255}
                    style={{width: 80, display: 'flex'}}
                    onChange={onChange}
                  />
                </Form.Item>
                <div> ) </div>
              </div>
            );
          case DorisTableFieldTypeMap.VARCHAR:
            return (
              <div className={styles['select_render_filed']}>
                <div> ( </div>
                <Form.Item
                  rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
                  name={[index, 'typeLength']}
                  initialValue={10}
                >
                  <InputNumber
                    disabled={!isAddTag && editTag}
                    min={1}
                    max={65533}
                    style={{width: 80, display: 'flex'}}
                    onChange={onChange}
                  />
                </Form.Item>
                <div> ) </div>
              </div>
            );
          case DorisTableFieldTypeMap.DECIMAL:
            return (
              <div className={styles['select_render_filed']}>
                <div> ( </div>
                <Form.Item
                  rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
                  name={[index, 'typePrecision']}
                  initialValue={10}
                >
                  <InputNumber
                    disabled={!isAddTag && editTag}
                    min={1}
                    max={38}
                    style={{width: 80, display: 'flex'}}
                    onChange={(v) => {
                      onChange();
                      setTypeScale(v);
                    }}
                  />
                </Form.Item>
                <div>,</div>
                <Form.Item
                  rules={[
                    {required: true, message: '请输入整数类型值', type: 'integer'},
                    {
                      validator: (_, value) => {
                        if (value > typeScale) {
                          return Promise.reject(new Error('小数位数不能大于精度'));
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                  name={[index, 'typeScale']}
                  initialValue={0}
                >
                  <InputNumber
                    disabled={!isAddTag && editTag}
                    min={0}
                    max={typeScale}
                    style={{width: 80, display: 'flex'}}
                    onChange={onChange}
                  />
                </Form.Item>
                <div> ) </div>
              </div>
            );
          case DorisTableFieldTypeMap.DATETIME:
            return (
              <div className={styles['select_render_filed']}>
                <div> ( </div>
                <Form.Item
                  rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
                  name={[index, 'typeScale']}
                  initialValue={0}
                >
                  <InputNumber
                    disabled={!isAddTag && editTag}
                    min={0}
                    max={6}
                    style={{width: 80, display: 'flex'}}
                    onChange={onChange}
                  />
                </Form.Item>
                <div> ) </div>
              </div>
            );
          case DorisTableFieldTypeMap.ARRAY:
            return (
              <div className={styles['select_render_filed']}>
                <Form.Item
                  rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
                  name={[index, 'typeText']}
                >
                  <Input
                    disabled={!isAddTag && editTag}
                    placeholder="<data_type>"
                    onChange={onChange}
                  ></Input>
                </Form.Item>
              </div>
            );
          case DorisTableFieldTypeMap.MAP:
            return (
              <Form.Item
                rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
                name={[index, 'typeText']}
              >
                <Input
                  disabled={!isAddTag && editTag}
                  placeholder="<primitive_type,data_type>"
                  onChange={onChange}
                ></Input>
              </Form.Item>
            );
          case DorisTableFieldTypeMap.STRUCT:
            return (
              <Form.Item
                rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
                name={[index, 'typeText']}
              >
                <Input
                  disabled={!isAddTag && editTag}
                  placeholder="<col_name:data_type [COMMENT col_comment],...>"
                  onChange={onChange}
                ></Input>
              </Form.Item>
            );
          case DorisTableFieldTypeMap.AGG_STATE:
            return (
              <Form.Item
                rules={[{required: true, message: '请输入数据内容', type: 'string'}]}
                name={[index, 'typeText']}
              >
                <Input
                  disabled={!isAddTag && editTag}
                  placeholder="<max_by(int/bight,int) or group_concat(varchar)>"
                  onChange={onChange}
                ></Input>
              </Form.Item>
            );
          default:
            return null;
        }
      }, [initialType, isAddTag, editTag, typeScale, index, onChange]);

      return (
        <div style={{display: 'flex', alignItems: 'center'}}>
          <Form.Item name={[index, 'typeName']}>
            <Select
              showSearch={true}
              disabled={(!isAddTag && editTag) || curItem.keyIndex !== undefined}
              style={{width: 150}}
              options={Object.entries(DorisTableFieldTypeMap).map(([key, value]) => ({
                label: key,
                value: value
              }))}
              placeholder="请选择类型"
              onChange={(value: any) => {
                onChange();
              }}
            />
          </Form.Item>
          <div className={styles['select_render_container']}>{typeMatchField}</div>
        </div>
      );
    },
    [dataInfo, form, form.getFieldsValue()?.columns, editTag, DorisTableFieldTypeMap, onChange]
  );

  // 主键计数器，主键位置按照索引位置进行存储
  const [keyCounter, setKeyCounter] = useState([]);
  const primaryKeyRenderComponent = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curColumns = formValues?.columns || [];
      const curItem = curColumns[index] || {};
      const [activeTag, setActiveTag] = useState(false);
      const selectType = curItem.typeName || DorisFieldTypes.STRING;
      const activeInitVal = useMemo(() => {
        return curItem.keyIndex !== undefined;
      }, [curItem]);
      // 类型是否包含在grayStatus中
      const typeInclude = grayStatus.includes(selectType);
      // 索引值
      const keyTag = useMemo(() => {
        return !activeInitVal ? keyCounter.indexOf(curItem.name) : curItem.keyIndex;
      }, [activeInitVal, curItem, keyCounter]);

      // 是不是新增加的行数据
      const isAddTag = dataInfo?.columns?.[index] ? false : true;

      // 1、新增状态下，字段类型包含在 grayStatus中，不允许点击修改主键字段类型，灰色显示；存在keyCounter中的需要进行删除。
      // 2、编辑状态下，字段设置了主键，不允许点击修改，灰色显示（新增行，不受限制）
      const primaryKeyContiner = useMemo(() => {
        if (typeInclude) {
          return (
            <div className={styles['table_primary-key_column']} style={{background: '#F5F6FB'}}>
              <IconSvg type="primary-key-gray" size={16} color="#F5F6FB" />
            </div>
          );
        }
        if (editTag && !isAddTag) {
          return (
            <div className={styles['table_primary-key_column']} style={{background: '#F5F6FB'}}>
              <IconSvg type="primary-key-gray" size={16} color="#F5F6FB" />
              {activeInitVal ? (
                <Form.Item name={[index, 'keyIndex']} initialValue={keyTag}>
                  <div style={{color: '#5C5F66', height: 20, width: 16, textAlign: 'center'}}>
                    {keyTag + 1}
                  </div>
                </Form.Item>
              ) : null}
            </div>
          );
        }
        return (
          <div
            className={styles['table_primary-key_column']}
            style={{background: activeTag || activeInitVal ? '#2468F2' : '#E6F0FF'}}
            onClick={(e) => {
              e.stopPropagation();
              if (!activeTag && !activeInitVal) {
                setActiveTag(true);
                setKeyCounter((pre) => [...pre, curItem.name]);
              } else {
                setActiveTag(false);
                if (keyTag !== -1) {
                  form.setFieldsValue({
                    columns: curColumns.map((item) => {
                      if (item.name === curItem.name) {
                        delete item.keyIndex;
                        return {...item};
                      }
                      item.keyIndex > keyTag && (item.keyIndex -= 1);
                      return item;
                    })
                  });
                  setKeyCounter((pre) => {
                    return [...pre.filter((item) => curItem.name !== item)];
                  });
                }
              }
              onChange();
            }}
          >
            <IconSvg
              size={16}
              type={activeTag || activeInitVal ? 'primary-key-white' : 'primary-key-blue'}
              color={activeTag || activeInitVal ? '#2468F2' : '#E6F0FF'}
            />
            {activeInitVal || activeTag ? (
              <Form.Item name={[index, 'keyIndex']} initialValue={keyTag}>
                <div style={{color: '#fff', height: 20, width: 16, textAlign: 'center'}}>{keyTag + 1}</div>
              </Form.Item>
            ) : null}
          </div>
        );
      }, [
        form,
        editTag,
        keyTag,
        isAddTag,
        activeTag,
        curItem,
        activeInitVal,
        setActiveTag,
        setKeyCounter,
        keyCounter,
        typeInclude
      ]);

      return (
        <Form.Item
          name={[index, 'primaryKey']}
          rules={[
            {
              message: '至少需要1个主键',
              validator: async () => {
                const formValues = form.getFieldsValue();
                const curColumns = formValues?.columns || [];
                if (!curColumns.some((item) => item.keyIndex !== undefined)) {
                  return Promise.reject();
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          {primaryKeyContiner}
        </Form.Item>
      );
    },
    [form, dataInfo, keyCounter, editTag, onChange]
  );

  const fieldUpdateToBucket = () => {
    const formValues = form.getFieldsValue();
    const curColumns = formValues?.columns || [];
    const bucket = formValues?.distribution?.columnNames || [];
    const newBucket = bucket.filter((item) => curColumns.some((citem) => citem.name === item)) || [];
    form.setFieldsValue({
      distribution: {
        ...formValues?.distribution,
        columnNames: [...newBucket]
      }
    });
  };

  const fieldNameRender = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curColumns = formValues?.columns || [];
      const name = curColumns[index]?.name || '';
      const bucket = formValues?.distribution?.columnNames || [];
      const partitioning = formValues?.partitioning || [];
      // 分区和分桶的内容不允许编辑
      const editTag = bucket.find((item) => item === name) || partitioning.find((item) => item.name === name);
      // 是不是新增加的行数据
      const isAddTag = dataInfo?.columns?.[index] ? false : true;
      return (
        <Form.Item
          name={[index, 'name']}
          rules={[
            {
              validator: async (_, value: string) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialNameStartEn128.test(value)) {
                  return Promise.reject(new Error(RULE.specialNameStartEn128Text));
                }
                if (
                  value &&
                  curColumns.some(
                    (v, vindex) => index !== vindex && v.name.toLowerCase() === value.toLowerCase()
                  )
                ) {
                  return Promise.reject('字段名称不能重复');
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input
            limitLength={128}
            disabled={!isAddTag && editTag}
            placeholder="请输入字段名称"
            onChange={() => {
              fieldUpdateToBucket();
              onChange();
            }}
          />
        </Form.Item>
      );
    },
    [dataInfo, onChange, editTag]
  );

  const fieldRemoveTag = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      // 非编辑模式
      if (!editTag) {
        return false;
      }
      const isAddTag = dataInfo?.columns?.[index] ? false : true;
      if (isAddTag) {
        return !isAddTag;
      }
      // 如果模型类型是 聚合模型 和 唯一模型，不允许删除操作
      const isDuplicate = formValues?.tableModel ? formValues?.tableModel === ETableDoris.DUPLICATE : false;
      if (!isDuplicate) {
        // 不允许删除主键列
        const isPrimaryKey = formValues?.columns?.[index]?.keyIndex !== undefined || false;
        if (isPrimaryKey) return true;
      }
      const name = formValues?.columns?.[index]?.name || '';
      const bucket = formValues?.distribution?.columnNames || [];
      const partitioning = formValues?.partitioning || [];
      // 分区和分桶的内容不允许编辑
      const inBucketOrPart =
        bucket.find((item) => item === name) || partitioning.find((item) => item.columnName === name);
      return inBucketOrPart;
    },
    [dataInfo]
  );

  const fieldDefaultRender = useCallback(
    (index) => {
      const isAddTag = dataInfo?.columns?.[index] ? false : true;
      return (
        <Form.Item name={[index, 'defaultValue']}>
          <Input
            disabled={!isAddTag && editTag}
            style={{width: 80}}
            placeholder="请输入"
            onChange={onChange}
          />
        </Form.Item>
      );
    },
    [dataInfo, onChange, editTag]
  );

  const aggFunRender = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const isPrimaryKey = formValues?.columns?.[index]?.keyIndex !== undefined || false;
      const isAddTag = dataInfo?.columns?.[index] ? false : true;
      return (
        <Form.Item
          name={[index, 'aggregateType']}
          rules={[
            {
              validator: async (rule, value) => {
                if (isPrimaryKey) {
                  form.resetFields([index, 'aggregateType']);
                  const formValuesTemp = form.getFieldsValue();
                  const curItem = formValuesTemp?.columns?.[index];
                  curItem.aggregateType = undefined;
                  return Promise.resolve();
                } else if (!value) {
                  return Promise.reject('Aggregate 聚合模型非主键列必须指定聚合函数');
                } else {
                  return Promise.resolve();
                }
              }
            }
          ]}
        >
          <Select
            allowClear
            showSearch
            disabled={isPrimaryKey || (editTag && !isAddTag)}
            style={{width: 180}}
            options={Object.entries(DorisAggFuncMap).map(([key, value]) => ({
              label: key,
              value: value
            }))}
            placeholder="请选择类型"
            onChange={() => {
              onChange();
            }}
          />
        </Form.Item>
      );
    },
    [form, editTag, bucketFields, DorisAggFuncMap, keyCounter, dataInfo, onChange]
  );

  const fieldColumnList = useMemo(() => {
    const baseColumnList = [
      {
        title: '字段名称',
        dataIndex: 'field_name',
        width: 100,
        render: (_, record, index) => fieldNameRender(index)
      },
      {
        title: '字段类型',
        dataIndex: 'field_typeName',
        render: (_, record, index) => selectTypeRenderComponent(index)
      },
      {
        title: '主键',
        width: 100,
        dataIndex: 'field_keyIndex',
        render: (_, record, index) => primaryKeyRenderComponent(index)
      },
      {
        title: '非空',
        width: 100,
        dataIndex: 'nullable',
        valuePropName: 'checked',
        renderComponent: <Checkbox onChange={onChange}></Checkbox>
      },
      {
        title: '默认值',
        dataIndex: 'field_defaultValue',
        render: (_, record, index) => fieldDefaultRender(index)
      },
      {
        title: '字段描述',
        dataIndex: 'comment',
        width: 200,
        renderComponent: <Input placeholder="请输入" onChange={onChange} limitLength={1024} />
      }
    ];

    if (modelType === ETableDoris.AGGREGATE) {
      const aggregateColumn: any = {
        title: '聚合函数',
        dataIndex: 'field_aggregateType',
        render: (_, record, index) => aggFunRender(index)
      };
      baseColumnList.splice(4, 0, aggregateColumn);
    }
    return baseColumnList;
  }, [modelType, dataInfo, keyCounter]);

  // 分区配置
  const partitionColumnList = [
    {
      title: '字段名称',
      dataIndex: 'columnName',
      rules: [{required: true, message: '请输入参数名称'}],
      renderComponent: <Input readOnly />
    }
  ];

  // 属性名称渲染
  const propsNameRender = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curProps = formValues?.properties || [];
      return (
        <Form.Item
          name={[index, 'name']}
          rules={[
            {required: true, message: '请输入属性名称'},
            {
              validator: async (_, value: string) => {
                if (
                  value &&
                  curProps.some(
                    (v, vindex) => index !== vindex && v.name.toLowerCase() === value.toLowerCase()
                  )
                ) {
                  return Promise.reject('属性名称不能重复');
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入" onChange={onChange} />
        </Form.Item>
      );
    },
    [form, onChange]
  );

  // 高级配置
  const configColumnList = [
    {
      title: '属性名称',
      dataIndex: 'name',
      render: (_, record, index) => propsNameRender(index)
    },
    {
      title: '属性值',
      dataIndex: 'value',
      rules: [{required: true, message: '请输入参数名称'}],
      renderComponent: <Input placeholder="请输入" onChange={onChange}></Input>
    }
  ];

  const bucketSelectColumnList = useMemo(() => {
    const formValues = form.getFieldsValue();
    const modelType = formValues?.tableModel || ETableDoris.DUPLICATE;
    const bucketFieldsTemp = bucketFields.filter((item) => item.name);
    if (modelType === ETableDoris.AGGREGATE || modelType === ETableDoris.UNIQUE) {
      return bucketFieldsTemp
        ?.filter((item) => item.keyIndex !== undefined)
        .map((item) => ({label: item.name, value: item.name}));
    }
    return bucketFieldsTemp?.map((item) => ({label: item.name, value: item.name}));
  }, [form, modelType, bucketFields]);

  const fieldDefaultValue = {name: '', typeName: DorisTableFieldTypeMap.STRING, nullable: false, comment: ''};
  const configDefaultValue = {name: '', value: ''};

  // 获取详情
  const {loading, run: getTableDetail} = useRequest(
    async () => {
      const res = await http.getTableDetail(workspaceId, fullName);
      if (res?.success) {
        setDataInfo(res?.result);
      }
    },
    {
      manual: true
    }
  );

  useEffect(() => {
    if (editTag) {
      // 拉取详情接口
      getTableDetail();
    }
  }, [editTag]);

  useEffect(() => {
    // 1、详情数据转化
    const formateData = detailDataFormate(dataInfo);
    // 2、表单回显
    form.setFieldsValue(formateData);
    // 3、keyCounter 初始化
    if (formateData?.columns && formateData?.columns.length > 0) {
      const hasKeyDatas = formateData.columns.filter((item) => item.keyIndex !== undefined);
      const initCounter: any = hasKeyDatas.map((_) => '');
      hasKeyDatas.forEach((item) => {
        initCounter[item.keyIndex] = item.name;
      });
      setKeyCounter(() => [...initCounter]);
    }
  }, [form, dataInfo, setKeyCounter]);

  useEffect(() => {
    form.setFieldsValue({columns: [{...fieldDefaultValue}]});
  }, []);

  useEffect(() => {
    const formValues = form.getFieldsValue();
    setModelType(formValues.tableModel || ETableDoris.DUPLICATE);
    if (keyCounter.length > 0) {
      return;
    }
    const columns = formValues.columns;
    if (columns && columns.length > 0) {
      const hasKeyDatas = columns.filter((item) => item.keyIndex !== undefined);
      const initCounter: any = hasKeyDatas.map((_) => '');
      hasKeyDatas.forEach((item) => {
        initCounter[item.keyIndex] = item.name;
      });
      setKeyCounter(() => [...initCounter]);
    }
  }, [form.getFieldsValue().tableModel, form.getFieldsValue().columns]);

  const removeKey = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curColumns = formValues?.columns || [];
      const curBucketColumns = formValues?.distribution?.columnNames || [];
      const newPre = keyCounter.filter((item) => curColumns.find((citem) => citem.name === item));
      form.setFieldsValue({
        columns: curColumns.map((item) => {
          if (!newPre.includes(item.name)) {
            delete item.keyIndex;
            return {...item};
          }
          item.keyIndex = newPre.indexOf(item.name);
          return item;
        }),
        distribution: {
          ...formValues?.distribution,
          columnNames: curBucketColumns.filter((item) => curColumns.find((citem) => citem.name === item))
        }
      });
      setKeyCounter((pre) => [...newPre]);
    },
    [form, keyCounter, setKeyCounter]
  );

  // 分桶方式值 - 监听变化
  const distributionType = Form.useWatch(['distribution', 'type'], form);

  const bucketCompentRender = () => {
    const formValues = form.getFieldsValue();
    const modelType = formValues?.tableModel || ETableDoris.DUPLICATE;

    return (
      <div>
        <Form.Item
          label="分桶方式"
          name={['distribution', 'type']}
          tooltip="Hash 分桶：按字段哈希分布，适用于按某字段频繁过滤、数据分布均匀的场景。Random 分桶：随机分布数据，适用于无明显查询维度、数据分布不均匀的场景。"
          initialValue={ETableDorisBucket.HASH}
        >
          <Radio.Group
            disabled={editTag}
            onChange={(v: any) => {
              form.setFieldValue(['distribution', 'type'], v.target.value);
              setTimeout(() => onChange(), 100);
            }}
          >
            {Object.keys(DorisTableBucketMap).map((key) => (
              <Radio
                value={key}
                key={key}
                disabled={modelType !== ETableDoris.DUPLICATE && key === ETableDorisBucket.RANDOM}
              >
                <span>{DorisTableBucketMap[key]}</span>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        {/* 分桶列在为：Hsah分桶时显示 */}
        {distributionType == ETableDorisBucket.HASH && (
          <Form.Item
            label="分桶列"
            name={['distribution', 'columnNames']}
            rules={[{required: true, message: '请选择分桶列'}]}
          >
            <Select
              style={{width: 200}}
              mode="multiple"
              options={bucketSelectColumnList}
              placeholder="请选择类型"
              onChange={onChange}
              disabled={editTag}
            />
          </Form.Item>
        )}
        <Form.Item
          label="分桶数"
          name={['distribution', 'number']}
          rules={[
            {
              required: true,
              message: '请输入分桶数',
              type: 'integer'
            }
          ]}
          initialValue={10}
        >
          <InputNumber
            disabled={editTag}
            style={{width: 200, display: 'flex'}}
            onChange={onChange}
            min={1}
            max={100}
          />
        </Form.Item>
      </div>
    );
  };

  return (
    <div className={styles['table_content_contailer']}>
      {loading && <Loading></Loading>}

      {!editTag && <LeftContentHeader status={status} errMsg={errMsg} onSilderHandle={onSilderHandle} />}

      <CreateTableBaseInfo
        dataInfo={dataInfo}
        onChange={onChange}
        tableHanleWay={tableHanleWay}
        tableFrom={TableSource.DORIS}
        onModelType={(v) => {
          setModelType(v);
        }}
      />

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>字段信息</div>
        <TableEditList
          showIndex={true}
          initRowData={true}
          isReadOnly={false}
          name="columns"
          form={form}
          columnList={fieldColumnList}
          removeTag={fieldRemoveTag}
          onRemove={(index: any) => {
            removeKey(index);
            onChange();
          }}
          defaultValue={fieldDefaultValue}
        />
      </div>

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>分区信息</div>
        <Alert
          icon={<IconSvg type="warning" size={18} />}
          message="由于分区配置功能较复杂，目前仅支持通过右侧DDL 编辑器进行编辑，此处仅展示从DDL中解析出的分区字段信息"
          type="info"
          showIcon
        />
        <div className={styles['item-table-partion']}>
          <TableEditList
            isReadOnly={true}
            showIndex={true}
            showControl={false}
            name="partitioning"
            form={form}
            onRemove={onChange}
            columnList={partitionColumnList}
          />
        </div>
      </div>

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>分桶信息</div>
        {bucketCompentRender()}
      </div>

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>
          <Tooltip title="Doris 仅支持预定义属性，使用自定义属性会导致建表失败。">高级配置</Tooltip>
        </div>
        <TableEditList
          isReadOnly={false}
          name="properties"
          form={form}
          columnList={configColumnList}
          defaultValue={configDefaultValue}
          onRemove={onChange}
          addButtonText="添加表属性"
        />
      </div>
    </div>
  );
};

export default MetaDorisTable;
